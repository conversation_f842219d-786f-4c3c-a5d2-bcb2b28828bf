import '../../../../core/utils/date_time_utils.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../averages/domain/repositories/per_reading_average_repository.dart';
import '../../../averages/domain/repositories/average_repository.dart';

class HybridCostService {
  final MeterReadingRepository _meterReadingRepository;
  final PerReadingAverageRepository _perReadingAverageRepository;
  final AverageRepository _averageRepository;

  const HybridCostService(
    this._meterReadingRepository,
    this._perReadingAverageRepository,
    this._averageRepository,
  );

  Future<double> calculateCustomPeriodCost(
      DateTime fromDate, DateTime toDate) async {
    final readings = await _meterReadingRepository.getAllMeterReadings();
    if (readings.isEmpty) return 0.0;

    readings.sort((a, b) => a.date.compareTo(b.date));

    // Find reading in period
    MeterReading? readingInPeriod;
    for (final reading in readings) {
      if (reading.date.isAfter(fromDate) &&
          reading.date.isBefore(toDate.add(const Duration(days: 1)))) {
        readingInPeriod = reading;
        break;
      }
    }

    if (readingInPeriod == null) {
      // Use total average fallback
      final average = await _averageRepository.getLatestAverages();
      if (average?.totalAverage == null) return 0.0;
      final days = DateTimeUtils.calculateDaysWithPrecision(fromDate, toDate);
      return average!.totalAverage! * days;
    }

    // Find next reading after period
    MeterReading? nextReading;
    for (final reading in readings) {
      if (reading.date.isAfter(toDate)) {
        nextReading = reading;
        break;
      }
    }

    if (nextReading == null) {
      // Use single reading's recent average for entire period
      final perReadingAverage = await _perReadingAverageRepository
          .getPerReadingAverageByMeterReadingId(readingInPeriod.id!);
      if (perReadingAverage == null) return 0.0;
      final days = DateTimeUtils.calculateDaysWithPrecision(fromDate, toDate);
      return perReadingAverage.recentAveragePerDay * days;
    }

    // Two-part calculation
    double totalCost = 0.0;

    // Part 1: fromDate to readingInPeriod.date
    final part1Average = await _perReadingAverageRepository
        .getPerReadingAverageByMeterReadingId(readingInPeriod.id!);
    if (part1Average != null) {
      final part1Days = DateTimeUtils.calculateDaysWithPrecision(
          fromDate, readingInPeriod.date);
      totalCost += part1Average.recentAveragePerDay * part1Days;
    }

    // Part 2: readingInPeriod.date to toDate
    final part2Average = await _perReadingAverageRepository
        .getPerReadingAverageByMeterReadingId(nextReading.id!);
    if (part2Average != null) {
      final part2Days = DateTimeUtils.calculateDaysWithPrecision(
          readingInPeriod.date, toDate);
      totalCost += part2Average.recentAveragePerDay * part2Days;
    }

    return totalCost;
  }


}
