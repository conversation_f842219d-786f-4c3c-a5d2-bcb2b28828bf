import '../../../../core/utils/date_time_utils.dart';
import '../../../../core/utils/logger.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../top_ups/domain/models/top_up.dart';
import '../../../../core/models/per_reading_average.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../top_ups/domain/repositories/top_up_repository.dart';
import '../../../averages/domain/repositories/per_reading_average_repository.dart';
import '../../../averages/domain/repositories/average_repository.dart';

/// Service for calculating electricity costs using hybrid approach
/// Uses recent averages for boundary intervals and direct differences for middle periods
class HybridCostService {
  final MeterReadingRepository _meterReadingRepository;
  final TopUpRepository _topUpRepository;
  final PerReadingAverageRepository _perReadingAverageRepository;
  final AverageRepository _averageRepository;

  const HybridCostService(
    this._meterReadingRepository,
    this._topUpRepository,
    this._perReadingAverageRepository,
    this._averageRepository,
  );

  /// Calculate cost for custom period using hybrid approach
  /// Uses recent averages for start/end intervals and direct differences for middle periods
  Future<double> calculateCustomPeriodCost(
    DateTime fromDate,
    DateTime toDate,
  ) async {
    try {
      Logger.info(
          'HybridCostService: Calculating cost for period ${fromDate.toIso8601String()} to ${toDate.toIso8601String()}');

      // Get all meter readings and top-ups
      final allMeterReadings =
          await _meterReadingRepository.getAllMeterReadings();
      final allTopUps = await _topUpRepository.getAllTopUps();

      if (allMeterReadings.isEmpty) {
        Logger.info('HybridCostService: No meter readings found');
        return 0.0;
      }

      // Sort readings by date
      allMeterReadings.sort((a, b) => a.date.compareTo(b.date));

      // Find readings that affect the period
      final relevantReadings =
          _findRelevantReadings(allMeterReadings, fromDate, toDate);

      if (relevantReadings.isEmpty) {
        Logger.info('HybridCostService: No relevant readings found for period');
        return await _calculateUsingTotalAverage(fromDate, toDate);
      }

      // Calculate cost using hybrid approach
      return await _calculateHybridCost(
          relevantReadings, allTopUps, fromDate, toDate);
    } catch (e) {
      Logger.error('HybridCostService: Error calculating cost: $e');
      return 0.0;
    }
  }

  /// Find meter readings that affect the calculation period
  List<MeterReading> _findRelevantReadings(
    List<MeterReading> allReadings,
    DateTime fromDate,
    DateTime toDate,
  ) {
    final relevantReadings = <MeterReading>[];

    // Find nearest reading before the period
    MeterReading? nearestBefore;
    for (final reading in allReadings) {
      if (reading.date.isBefore(fromDate)) {
        nearestBefore = reading;
      } else {
        break;
      }
    }

    // Add nearest before if exists
    if (nearestBefore != null) {
      relevantReadings.add(nearestBefore);
    }

    // Add readings within or affecting the period
    for (final reading in allReadings) {
      if (reading.date.isAfter(fromDate.subtract(const Duration(seconds: 1))) &&
          reading.date.isBefore(toDate.add(const Duration(days: 1)))) {
        relevantReadings.add(reading);
      }
    }

    // Find first reading after the period if needed
    for (final reading in allReadings) {
      if (reading.date.isAfter(toDate)) {
        relevantReadings.add(reading);
        break;
      }
    }

    return relevantReadings;
  }

  /// Calculate cost using hybrid approach
  Future<double> _calculateHybridCost(
    List<MeterReading> relevantReadings,
    List<TopUp> allTopUps,
    DateTime fromDate,
    DateTime toDate,
  ) async {
    double totalCost = 0.0;

    // If only one reading, use recent average for entire period
    if (relevantReadings.length == 1) {
      return await _calculateSingleReadingCost(
          relevantReadings.first, fromDate, toDate);
    }

    // Process intervals between readings
    for (int i = 0; i < relevantReadings.length - 1; i++) {
      final currentReading = relevantReadings[i];
      final nextReading = relevantReadings[i + 1];

      final intervalCost = await _calculateIntervalCost(
        currentReading,
        nextReading,
        allTopUps,
        fromDate,
        toDate,
      );

      totalCost += intervalCost;
    }

    Logger.info('HybridCostService: Total calculated cost: $totalCost');
    return totalCost;
  }

  /// Calculate cost for interval between two readings
  Future<double> _calculateIntervalCost(
    MeterReading currentReading,
    MeterReading nextReading,
    List<TopUp> allTopUps,
    DateTime fromDate,
    DateTime toDate,
  ) async {
    // Determine the actual interval within the period
    final intervalStart =
        currentReading.date.isBefore(fromDate) ? fromDate : currentReading.date;
    final intervalEnd =
        nextReading.date.isAfter(toDate) ? toDate : nextReading.date;

    // Skip if interval is outside the period
    if (intervalStart.isAfter(intervalEnd)) {
      return 0.0;
    }

    // Check if this is a boundary interval requiring recent average
    final isStartBoundary = currentReading.date.isBefore(fromDate);
    final isEndBoundary = nextReading.date.isAfter(toDate);

    if (isStartBoundary || isEndBoundary) {
      // Use recent average for boundary intervals
      return await _calculateRecentAverageInterval(
        nextReading,
        intervalStart,
        intervalEnd,
      );
    } else {
      // Use direct difference for middle periods
      return _calculateDirectDifference(
        currentReading,
        nextReading,
        allTopUps,
        intervalStart,
        intervalEnd,
      );
    }
  }

  /// Calculate cost using recent average for boundary intervals
  Future<double> _calculateRecentAverageInterval(
    MeterReading reading,
    DateTime intervalStart,
    DateTime intervalEnd,
  ) async {
    // Get recent average for this reading
    final perReadingAverage = await _perReadingAverageRepository
        .getPerReadingAverageByMeterReadingId(reading.id!);

    if (perReadingAverage == null) {
      Logger.warning(
          'HybridCostService: No recent average found for reading ${reading.id}');
      return 0.0;
    }

    // Calculate time fraction
    final timeFraction =
        DateTimeUtils.calculateTimeFractionOfDay(intervalStart, intervalEnd);
    final cost = perReadingAverage.recentAveragePerDay * timeFraction;

    Logger.info(
        'HybridCostService: Recent average interval cost: $cost (${timeFraction.toStringAsFixed(4)} days × ${perReadingAverage.recentAveragePerDay.toStringAsFixed(4)}/day)');
    return cost;
  }

  /// Calculate cost using direct meter reading difference
  double _calculateDirectDifference(
    MeterReading startReading,
    MeterReading endReading,
    List<TopUp> allTopUps,
    DateTime intervalStart,
    DateTime intervalEnd,
  ) {
    // Find top-ups between the readings
    final topUpsBetween = allTopUps
        .where((topUp) =>
            topUp.date.isAfter(startReading.date) &&
            topUp.date.isBefore(endReading.date))
        .toList();

    final totalTopUps =
        topUpsBetween.fold(0.0, (sum, topUp) => sum + topUp.amount);

    // Calculate usage: (start_balance + top_ups) - end_balance
    final usage = (startReading.value + totalTopUps) - endReading.value;

    // Calculate the proportion of this interval that falls within our period
    final totalIntervalDays = DateTimeUtils.calculateDaysWithPrecision(
        startReading.date, endReading.date);
    final periodIntervalDays =
        DateTimeUtils.calculateDaysWithPrecision(intervalStart, intervalEnd);

    final proportion =
        totalIntervalDays > 0 ? periodIntervalDays / totalIntervalDays : 0.0;
    final cost = usage * proportion;

    Logger.info(
        'HybridCostService: Direct difference cost: $cost (usage: $usage × proportion: ${proportion.toStringAsFixed(4)})');
    return cost > 0 ? cost : 0.0;
  }

  /// Calculate cost for single reading using recent average
  Future<double> _calculateSingleReadingCost(
    MeterReading reading,
    DateTime fromDate,
    DateTime toDate,
  ) async {
    final perReadingAverage = await _perReadingAverageRepository
        .getPerReadingAverageByMeterReadingId(reading.id!);

    if (perReadingAverage == null) {
      Logger.warning(
          'HybridCostService: No recent average found for single reading ${reading.id}');
      return await _calculateUsingTotalAverage(fromDate, toDate);
    }

    final timeFraction =
        DateTimeUtils.calculateTimeFractionOfDay(fromDate, toDate);
    final cost = perReadingAverage.recentAveragePerDay * timeFraction;

    Logger.info('HybridCostService: Single reading cost: $cost');
    return cost;
  }

  /// Fallback to total average calculation
  Future<double> _calculateUsingTotalAverage(
      DateTime fromDate, DateTime toDate) async {
    final average = await _averageRepository.getLatestAverages();
    if (average?.totalAverage == null) {
      Logger.warning('HybridCostService: No total average available');
      return 0.0;
    }

    final days = DateTimeUtils.calculateDaysWithPrecision(fromDate, toDate);
    final cost = average!.totalAverage! * days;

    Logger.info('HybridCostService: Total average fallback cost: $cost');
    return cost;
  }
}
