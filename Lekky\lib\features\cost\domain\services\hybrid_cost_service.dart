import '../../../../core/utils/date_time_utils.dart';
import '../../../../core/utils/logger.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../averages/domain/repositories/per_reading_average_repository.dart';
import '../../../averages/domain/repositories/average_repository.dart';

/// Service for calculating electricity costs using hybrid approach
/// Uses recent averages for boundary intervals and direct differences for middle periods
class HybridCostService {
  final MeterReadingRepository _meterReadingRepository;
  final TopUpRepository _topUpRepository;
  final PerReadingAverageRepository _perReadingAverageRepository;
  final AverageRepository _averageRepository;

  const HybridCostService(
    this._meterReadingRepository,
    this._topUpRepository,
    this._perReadingAverageRepository,
    this._averageRepository,
  );

  /// Calculate cost for custom period using hybrid approach
  /// Uses recent averages for start/end intervals and direct differences for middle periods
  Future<double> calculateCustomPeriodCost(
    DateTime fromDate,
    DateTime toDate,
  ) async {
    try {
      Logger.info(
          'HybridCostService: Calculating cost for period ${fromDate.toIso8601String()} to ${toDate.toIso8601String()}');

      // Get all meter readings
      final allMeterReadings =
          await _meterReadingRepository.getAllMeterReadings();

      if (allMeterReadings.isEmpty) {
        Logger.info('HybridCostService: No meter readings found');
        return 0.0;
      }

      // Sort readings by date
      allMeterReadings.sort((a, b) => a.date.compareTo(b.date));

      // Find reading within or immediately after period start
      final readingInPeriod =
          _findReadingInPeriod(allMeterReadings, fromDate, toDate);

      if (readingInPeriod == null) {
        Logger.info(
            'HybridCostService: No reading found in period, using total average');
        return await _calculateUsingTotalAverage(fromDate, toDate);
      }

      // Find next reading after the period for second part calculation
      final nextReading = _findNextReadingAfterPeriod(allMeterReadings, toDate);

      if (nextReading == null) {
        Logger.info(
            'HybridCostService: No next reading found, using single reading calculation');
        return await _calculateSingleReadingCost(
            readingInPeriod, fromDate, toDate);
      }

      // Calculate two-part cost
      return await _calculateTwoPartCost(
          fromDate, toDate, readingInPeriod, nextReading);
    } catch (e) {
      Logger.error('HybridCostService: Error calculating cost: $e');
      return 0.0;
    }
  }

  /// Find reading within the period or immediately after period start
  MeterReading? _findReadingInPeriod(
    List<MeterReading> allReadings,
    DateTime fromDate,
    DateTime toDate,
  ) {
    // Look for reading within the period first
    for (final reading in allReadings) {
      if (reading.date.isAfter(fromDate) &&
          reading.date.isBefore(toDate.add(const Duration(days: 1)))) {
        return reading;
      }
    }

    // If no reading in period, find first reading after period start
    for (final reading in allReadings) {
      if (reading.date.isAfter(fromDate)) {
        return reading;
      }
    }

    return null;
  }

  /// Find next reading after the period end
  MeterReading? _findNextReadingAfterPeriod(
    List<MeterReading> allReadings,
    DateTime toDate,
  ) {
    for (final reading in allReadings) {
      if (reading.date.isAfter(toDate)) {
        return reading;
      }
    }
    return null;
  }

  /// Calculate two-part cost using recent averages
  Future<double> _calculateTwoPartCost(
    DateTime fromDate,
    DateTime toDate,
    MeterReading readingInPeriod,
    MeterReading nextReading,
  ) async {
    double totalCost = 0.0;

    // Part 1: fromDate to readingInPeriod.date using readingInPeriod's recent average
    final part1Cost = await _calculateRecentAverageInterval(
      readingInPeriod,
      fromDate,
      readingInPeriod.date,
    );
    totalCost += part1Cost;

    // Part 2: readingInPeriod.date to toDate using nextReading's recent average
    final part2Cost = await _calculateRecentAverageInterval(
      nextReading,
      readingInPeriod.date,
      toDate,
    );
    totalCost += part2Cost;

    Logger.info(
        'HybridCostService: Two-part cost calculation - Part 1: $part1Cost, Part 2: $part2Cost, Total: $totalCost');
    return totalCost;
  }

  /// Calculate cost using recent average for boundary intervals
  Future<double> _calculateRecentAverageInterval(
    MeterReading reading,
    DateTime intervalStart,
    DateTime intervalEnd,
  ) async {
    // Get recent average for this reading
    final perReadingAverage = await _perReadingAverageRepository
        .getPerReadingAverageByMeterReadingId(reading.id!);

    if (perReadingAverage == null) {
      Logger.warning(
          'HybridCostService: No recent average found for reading ${reading.id}');
      return 0.0;
    }

    // Calculate time fraction
    final timeFraction =
        DateTimeUtils.calculateTimeFractionOfDay(intervalStart, intervalEnd);
    final cost = perReadingAverage.recentAveragePerDay * timeFraction;

    Logger.info(
        'HybridCostService: Recent average interval cost: $cost (${timeFraction.toStringAsFixed(4)} days × ${perReadingAverage.recentAveragePerDay.toStringAsFixed(4)}/day)');
    return cost;
  }

  /// Calculate cost for single reading using recent average
  Future<double> _calculateSingleReadingCost(
    MeterReading reading,
    DateTime fromDate,
    DateTime toDate,
  ) async {
    final perReadingAverage = await _perReadingAverageRepository
        .getPerReadingAverageByMeterReadingId(reading.id!);

    if (perReadingAverage == null) {
      Logger.warning(
          'HybridCostService: No recent average found for single reading ${reading.id}');
      return await _calculateUsingTotalAverage(fromDate, toDate);
    }

    final timeFraction =
        DateTimeUtils.calculateTimeFractionOfDay(fromDate, toDate);
    final cost = perReadingAverage.recentAveragePerDay * timeFraction;

    Logger.info('HybridCostService: Single reading cost: $cost');
    return cost;
  }

  /// Fallback to total average calculation
  Future<double> _calculateUsingTotalAverage(
      DateTime fromDate, DateTime toDate) async {
    final average = await _averageRepository.getLatestAverages();
    if (average?.totalAverage == null) {
      Logger.warning('HybridCostService: No total average available');
      return 0.0;
    }

    final days = DateTimeUtils.calculateDaysWithPrecision(fromDate, toDate);
    final cost = average!.totalAverage! * days;

    Logger.info('HybridCostService: Total average fallback cost: $cost');
    return cost;
  }
}
