import '../../../../core/utils/date_time_utils.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../top_ups/domain/repositories/top_up_repository.dart';
import '../../../averages/domain/repositories/average_repository.dart';

class HybridCostService {
  final MeterReadingRepository _meterReadingRepository;
  final TopUpRepository _topUpRepository;
  final AverageRepository _averageRepository;

  const HybridCostService(
    this._meterReadingRepository,
    this._topUpRepository,
    this._averageRepository,
  );

  Future<double> calculateCustomPeriodCost(
      DateTime fromDate, DateTime toDate) async {
    final readings = await _meterReadingRepository.getAllMeterReadings();
    final topUps = await _topUpRepository.getAllTopUps();

    if (readings.length < 2) {
      final average = await _averageRepository.getLatestAverages();
      if (average?.totalAverage == null) return 0.0;
      final days = DateTimeUtils.calculateDaysWithPrecision(fromDate, toDate);
      return average!.totalAverage! * days;
    }

    readings.sort((a, b) => a.date.compareTo(b.date));

    // Find readings that span the period
    final firstReading = readings.first;
    final lastReading = readings.last;

    // Calculate total usage accounting for top-ups
    double totalTopUps = 0.0;
    for (final topUp in topUps) {
      if (topUp.date.isAfter(firstReading.date) &&
          topUp.date.isBefore(lastReading.date)) {
        totalTopUps += topUp.amount;
      }
    }

    final totalUsage = (firstReading.value - lastReading.value) + totalTopUps;
    final totalDays = lastReading.date.difference(firstReading.date).inDays;

    if (totalDays <= 0 || totalUsage <= 0) {
      final average = await _averageRepository.getLatestAverages();
      if (average?.totalAverage == null) return 0.0;
      final days = DateTimeUtils.calculateDaysWithPrecision(fromDate, toDate);
      return average!.totalAverage! * days;
    }

    final dailyAverage = totalUsage / totalDays;
    final periodDays =
        DateTimeUtils.calculateDaysWithPrecision(fromDate, toDate);

    return dailyAverage * periodDays;
  }
}
